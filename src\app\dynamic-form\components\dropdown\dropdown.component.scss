@use '../../../../styles/shared.scss' as *;

/* Dropdown Input Container - Full width with arrow button */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 8px;
}

/* Base form input styling - Seamlessly integrated with arrow button */
.dropdown-input-container .form-input {
  flex: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px 0 0 8px;
  border-right: none;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 8px 12px;
  outline: none;
  box-sizing: border-box;
}

/* Focus state for entire container - input and button as one component with rounded shadow */
.dropdown-input-container:focus-within {
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  border-radius: 8px; /* Ensure shadow follows the rounded border shape */
}

.dropdown-input-container:focus-within .form-input {
  outline: none;
  border-color: #9e9e9e;
}

.dropdown-input-container:focus-within .dropdown-arrow-btn {
  border-color: #9e9e9e;
}

/* Remove individual input focus styles since container handles it */
.dropdown-input-container .form-input:focus {
  outline: none;
  box-shadow: none;
}

/* Invalid input state for entire container - input and button as one component */
.dropdown-input-container:has(.form-input.invalid-input) {
  box-shadow: none; /* Remove default shadow for invalid state */
  border-radius: 8px; /* Maintain rounded corners for invalid state */
}

.dropdown-input-container:has(.form-input.invalid-input):focus-within {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
  border-radius: 8px; /* Ensure invalid focus shadow follows rounded border shape */
}

.dropdown-input-container:has(.form-input.invalid-input) .form-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-input-container:has(.form-input.invalid-input) .dropdown-arrow-btn {
  border-color: #f44336;
}

/* Fallback for browsers that don't support :has() */
.dropdown-input-container .form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.dropdown-input-container .form-input.invalid-input:focus {
  box-shadow: none; /* Container handles the shadow */
}

.dropdown-input-container .form-input.invalid-input + .dropdown-arrow-btn {
  border-color: #f44336;
}

/* Placeholder styling - EXACT from backup lines 336-338 */
.dropdown-input-container .form-input::placeholder {
  color: #999;
}

/* Dropdown arrow button - Perfectly attached to textbox end */
.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  margin-left: -1px; /* Overlap with input border for seamless attachment */
  position: relative; /* Changed from absolute to relative for better integration */
  flex-shrink: 0; /* Prevent button from shrinking */
}

.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

/* Arrow button focus state - integrated with container focus */
.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: none; /* Container handles the focus shadow */
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Enhanced Dropdown List Styles - Perfect form color matching */
.dropdown-list {
  position: absolute;
  top: 100%; /* No gap - seamlessly connected */
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* Clean white to subtle gray gradient */
  border: 2px solid #e2e8f0; /* Softer border matching form fields */
  border-top: none; /* Remove top border for seamless connection */
  border-radius: 0 0 12px 12px; /* Only bottom corners rounded */
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.08),
    0 10px 20px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Enhanced layered shadows with inner highlight */
  height: 300px; /* FIXED height - not max-height */
  overflow-y: scroll; /* ALWAYS show vertical scrollbar */
  overflow-x: hidden; /* Prevent horizontal scroll */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: auto; /* Firefox - visible scrollbar */
  scrollbar-color: #64748b #f1f5f9; /* Enhanced scrollbar colors matching form */
  backdrop-filter: blur(16px) saturate(200%); /* Enhanced glass effect */
  padding: 0 !important; /* ABSOLUTELY NO PADDING */
  margin: 0 !important; /* ABSOLUTELY NO MARGINS */
}

/* Enhanced dropdown items - Proper text display */
.dropdown-item {
  padding: 14px 12px; /* Balanced padding for better text display */
  cursor: pointer;
  border-bottom: 1px solid #e2e8f0; /* Softer border matching form */
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #475569; /* Darker, more readable - matches form text */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth modern transition */
  outline: none;
  white-space: normal; /* Allow text wrapping for better display */
  overflow: visible; /* Show full content */
  text-overflow: clip; /* Don't truncate short values */
  min-height: 48px; /* Taller for better touch targets */
  display: flex;
  align-items: center;
  letter-spacing: 0.01em;
  margin: 0; /* No margins */
  background: transparent; /* Transparent to show container background */
}

/* Enhanced hover states - Perfect form-matching background */
.dropdown-item:hover,
.dropdown-item:focus {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); /* Enhanced blue gradient matching form focus */
  color: #1e40af; /* Darker blue text for better contrast */
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 2px 4px rgba(59, 130, 246, 0.1); /* Enhanced layered shadows */
}

/* First dropdown item - Proper edge attachment */
.dropdown-item:first-child {
  padding-left: 8px; /* Minimal left padding */
  border-radius: 10px 10px 0 0; /* Round top corners */
}

/* Last dropdown item - Proper edge attachment */
.dropdown-item:last-child {
  padding-right: 8px; /* Minimal right padding */
  border-bottom: none;
  border-radius: 0 0 10px 10px; /* Round bottom corners */
}

.dropdown-item:active {
  background-color: #e9ecef;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dropdown-loading .mat-icon {
  animation: spin 1s linear infinite;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled State Styling - EXACT from backup implementation */
.dropdown-input-container.disabled .form-input,
.dropdown-input-container .form-input.disabled,
.dropdown-input-container .form-input[disabled],
.dropdown-input-container .form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn,
.dropdown-input-container .dropdown-arrow-btn[disabled] {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn .mat-icon,
.dropdown-input-container .dropdown-arrow-btn[disabled] .mat-icon {
  color: #5f6368 !important;
}

/* Ensure dropdown works well in different contexts - EXACT from backup */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}

/* Ensure proper styling inheritance for all form-input elements */
.form-input {
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  outline: none;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.form-input.invalid-input:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.form-input::placeholder {
  color: #999;
}

/* Responsive dropdown styling - EXACT from regular-field component lines 427-491 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-loading {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-loading {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* ALWAYS VISIBLE scrollbar styling for original dropdown */
.dropdown-list::-webkit-scrollbar {
  width: 12px !important; /* Wider for better visibility */
  background: #f8f9fa !important; /* Light background */
  display: block !important; /* Force display */
  -webkit-appearance: none !important; /* Remove default styling */
}

.dropdown-list::-webkit-scrollbar-track {
  background: #e9ecef !important; /* Visible track */
  border-radius: 6px;
  margin: 2px;
  border: 1px solid #dee2e6; /* Border for definition */
  display: block !important; /* Force display */
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #888888 !important; /* Darker for visibility */
  border-radius: 6px;
  border: 2px solid #e9ecef; /* Border for definition */
  min-height: 30px !important; /* Larger minimum thumb size */
  display: block !important; /* Force display */
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #666666 !important; /* Darker on hover */
}

.dropdown-list::-webkit-scrollbar-thumb:active {
  background: #444444 !important; /* Even darker when active */
}

/* Force scrollbar visibility across browsers */
.dropdown-list {
  scrollbar-gutter: stable;
  /* Force scrollbar to always be present */
  padding-right: 0; /* Remove any padding that might hide scrollbar */
}

/* Force webkit scrollbar to be visible */
.dropdown-list::-webkit-scrollbar {
  -webkit-appearance: none;
  display: block !important;
}

/* Scrollbars are always rendered regardless of content amount */

/* FORCE scrollbar track and thumb to show even with minimal content */
.dropdown-list::-webkit-scrollbar-track {
  background: #e9ecef !important;
  display: block !important;
  visibility: visible !important;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #888888 !important;
  display: block !important;
  visibility: visible !important;
  /* Force thumb to appear even when no scrolling needed */
  min-height: 50px !important;
}

/* Fallback for Firefox and other browsers */
@supports not selector(::-webkit-scrollbar) {
  .dropdown-list {
    scrollbar-width: auto; /* Use browser default scrollbar width */
    scrollbar-color: #c1c1c1 #f1f1f1;
  }
}

/* Force scrollbar visibility - Debug styles */
.dropdown-list {
  /* Force minimum content height to trigger scrollbar */
  min-height: 100px;
}

/* Ensure scrollbar is always visible even with few items */
.dropdown-list::-webkit-scrollbar {
  -webkit-appearance: none; /* Remove default styling */
  display: block !important; /* Force display */
}

.dropdown-list::-webkit-scrollbar-track {
  -webkit-appearance: none;
  display: block !important;
}

.dropdown-list::-webkit-scrollbar-thumb {
  -webkit-appearance: none;
  display: block !important;
}

/* Mobile scrollbar styling for original dropdown */
@media (max-width: 768px) {
  .dropdown-list::-webkit-scrollbar {
    width: 10px;
  }
}

@media (max-width: 480px) {
  .dropdown-list::-webkit-scrollbar {
    width: 8px;
  }
}

/* Ensure dropdown containers work well in multi-field and grouped contexts - EXACT from regular-field component lines 523-532 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}

/* Responsive Design for Dropdowns */
@media (max-width: 768px) {
  .dropdown-list {
    max-height: 250px; /* Slightly smaller on tablets */
  }

  .dropdown-item {
    padding: 10px 12px; /* Slightly smaller padding */
    font-size: 14px;
  }

  .dropdown-input-container {
    min-width: 150px; /* Reduce minimum width on smaller screens */
  }

  .dropdown-input-container .form-input {
    font-size: 14px;
    padding: 6px 10px;
  }

  .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-arrow-btn .mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}

@media (max-width: 480px) {
  .dropdown-list {
    max-height: 200px; /* Smaller on mobile */
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 13px;
  }

  .dropdown-input-container {
    min-width: 120px;
  }

  .dropdown-input-container .form-input {
    font-size: 13px;
    padding: 4px 8px;
    height: 36px;
  }

  .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-arrow-btn .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 360px) {
  .dropdown-list {
    max-height: 180px; /* Even smaller on very small screens */
  }

  .dropdown-item {
    padding: 6px 8px;
    font-size: 12px;
  }

  .dropdown-input-container {
    min-width: 100px;
  }

  .dropdown-input-container .form-input {
    font-size: 12px;
    padding: 2px 6px;
    height: 32px;
  }

  .dropdown-arrow-btn {
    width: 28px;
    height: 28px;
  }

  .dropdown-arrow-btn .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

/* ULTIMATE FORCE SCROLLBAR VISIBILITY - Override ALL hiding */
.dropdown-list {
  /* Force scrollbar to be visible on all systems */
  scrollbar-width: auto !important; /* Firefox */
  -ms-overflow-style: scrollbar !important; /* IE/Edge */
  /* Override macOS/iOS scrollbar hiding */
  -webkit-overflow-scrolling: auto !important;
  /* Force scrollbar to never hide */
  overflow-y: scroll !important;
  /* Override any CSS that might hide scrollbars */
  scrollbar-gutter: stable both-edges !important;
}

/* Force scrollbar elements to be visible */
.dropdown-list::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-thumb {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  pointer-events: auto !important;
}
