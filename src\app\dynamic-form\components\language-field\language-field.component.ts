import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthenticationService } from '../../../services/authentication.service';

@Component({
  selector: 'app-language-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './language-field.component.html',
  styleUrl: './language-field.component.scss'
})
export class LanguageFieldComponent implements OnInit, OnDestroy {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  
  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  private authService = inject(AuthenticationService);
  
  // Language management
  availableLanguages: string[] = [];
  currentLanguageIndex: number = 0;
  languageControls: { [key: string]: FormControl } = {};
  showLanguageSelector: boolean = false;
  showLanguagePopup: boolean = false;

  ngOnInit(): void {
    this.initializeLanguageField();
    
    // Auto-sync with form values after initialization
    setTimeout(() => {
      this.syncWithFormValues();
    }, 100);
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private initializeLanguageField(): void {
    // Get available languages from authentication service
    this.availableLanguages = this.authService.getUserLanguages();
    
    // Create form controls for each language
    this.createLanguageControls();
    
    // Set up the main form control
    this.setupMainFormControl();
    
    // Show language selector only if multiple languages available
    this.showLanguageSelector = this.availableLanguages.length > 1;
  }

  private createLanguageControls(): void {
    const validators = this.field.mandatory ? [Validators.required] : [];
    
    this.availableLanguages.forEach(lang => {
      const controlName = `${this.field.fieldName}_${lang}`;
      
      // Check if the control already exists in the form
      let existingControl = this.form.get(controlName);
      
      if (existingControl) {
        // Use the existing control
        this.languageControls[lang] = existingControl as FormControl;
      } else {
        // Create a new control and add it to the form
        this.languageControls[lang] = new FormControl('', validators);
        this.form.addControl(controlName, this.languageControls[lang]);
      }
      
      // Disable if in view mode or field has noInput
      if (this.isViewMode || this.field.noInput) {
        this.languageControls[lang].disable({ emitEvent: false });
      }
    });
  }

  private setupMainFormControl(): void {
    // Ensure main form control exists
    if (!this.form.get(this.field.fieldName)) {
      const validators = this.field.mandatory ? [Validators.required] : [];
      const mainControl = new FormControl('', validators);
      this.form.addControl(this.field.fieldName, mainControl);

      // Disable if in view mode or field has noInput
      if (this.isViewMode || this.field.noInput) {
        mainControl.disable({ emitEvent: false });
      }
    }

    // Set initial value from first language (default)
    this.syncMainControlWithDefaultLanguage();
  }

  toggleLanguage(): void {
    if (this.availableLanguages.length <= 1) return;

    this.currentLanguageIndex = (this.currentLanguageIndex + 1) % this.availableLanguages.length;
    // Note: Main control always syncs with default language (first), not current language
  }

  private syncMainControlWithDefaultLanguage(): void {
    // Always sync with first language (default)
    const defaultLanguage = this.availableLanguages[0];
    const defaultLangControl = this.languageControls[defaultLanguage];
    const mainControl = this.form.get(this.field.fieldName);

    if (defaultLangControl && mainControl) {
      // Update main control with default language value
      mainControl.setValue(defaultLangControl.value, { emitEvent: false });
    }
  }

  onLanguageInputChange(language: string, value: any): void {
    // Update the language-specific control
    if (this.languageControls[language]) {
      this.languageControls[language].setValue(value, { emitEvent: false });
    }

    // Always update the main control with the default language value
    const defaultLanguage = this.availableLanguages[0];
    const defaultLangControl = this.languageControls[defaultLanguage];
    const mainControl = this.form.get(this.field.fieldName);
    
    if (defaultLangControl && mainControl) {
      mainControl.setValue(defaultLangControl.value, { emitEvent: false });
    }

    // Emit field value change for main field
    this.fieldValueChange.emit({
      fieldName: this.field.fieldName,
      value: mainControl?.value
    });
  }

  getCurrentLanguage(): string {
    return this.availableLanguages[this.currentLanguageIndex] || 'en';
  }

  getCurrentLanguageControl(): FormControl {
    const currentLang = this.getCurrentLanguage();
    return this.languageControls[currentLang];
  }

  getMainFormControl(): FormControl {
    return this.form.get(this.field.fieldName) as FormControl;
  }

  getInputType(): string {
    switch (this.field.type) {
      case 'int':
      case 'double':
        return 'number';
      case 'date':
        return 'date';
      case 'password':
        return 'password';
      default:
        return 'text';
    }
  }

  toggleLanguagePopup(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.showLanguagePopup = !this.showLanguagePopup;

    if (this.showLanguagePopup) {
      // Add drag functionality after popup is shown
      setTimeout(() => {
        this.addDragFunctionality();
      }, 100);
    }
  }

  closeLanguagePopup(): void {
    this.showLanguagePopup = false;
  }

  saveLanguageChanges(): void {
    // Update main control with first language value (default)
    const firstLanguage = this.availableLanguages[0];
    if (firstLanguage && this.languageControls[firstLanguage]) {
      const mainControl = this.getMainFormControl();
      if (mainControl) {
        mainControl.setValue(this.languageControls[firstLanguage].value, { emitEvent: false });
      }
    }

    // Emit field value change for the main field
    this.fieldValueChange.emit({
      fieldName: this.field.fieldName,
      value: this.getMainFormControl().value
    });

    this.closeLanguagePopup();
  }

  getLanguageDisplayName(langCode: string): string {
    const languageNames: { [key: string]: string } = {
      'en': 'English',
      'ar': 'العربية',
      'fr': 'Français',
      'es': 'Español',
      'de': 'Deutsch'
    };
    return languageNames[langCode] || langCode.toUpperCase();
  }

  getLanguageTooltip(): string {
    if (this.availableLanguages.length === 0) {
      return 'Edit in multiple languages';
    }
    
    // Show only the default language (first in array) abbreviation
    const defaultLanguage = this.availableLanguages[0];
    return defaultLanguage.toUpperCase();
  }

  getUniqueFieldId(): string {
    const currentLang = this.getCurrentLanguage();
    return `${this.field.fieldName}_${currentLang}_input`;
  }

  // Method to populate language fields with data
  populateLanguageFields(data: any): void {
    this.availableLanguages.forEach(lang => {
      const langFieldName = `${this.field.fieldName}_${lang}`;
      if (data[langFieldName] !== undefined && this.languageControls[lang]) {
        this.languageControls[lang].setValue(data[langFieldName], { emitEvent: false });
      }
    });

    // Update main control with default language value (first language)
    this.syncMainControlWithDefaultLanguage();
  }

  // Method to sync with current form values
  syncWithFormValues(): void {
    this.availableLanguages.forEach(lang => {
      const langFieldName = `${this.field.fieldName}_${lang}`;
      const formControl = this.form.get(langFieldName);
      
      if (formControl && this.languageControls[lang]) {
        this.languageControls[lang].setValue(formControl.value, { emitEvent: false });
      }
    });

    // Sync main control
    this.syncMainControlWithDefaultLanguage();
  }

  // Public method to trigger sync (can be called externally)
  public triggerSync(): void {
    this.syncWithFormValues();
  }

  // Method to get all language field values
  getLanguageFieldValues(): { [key: string]: any } {
    const values: { [key: string]: any } = {};
    this.availableLanguages.forEach(lang => {
      const control = this.languageControls[lang];
      if (control) {
        values[`${this.field.fieldName}_${lang}`] = control.value;
      }
    });
    return values;
  }

  // Add drag functionality - Match Query Popup Implementation
  private addDragFunctionality(): void {
    const popup = document.querySelector('.language-popup') as HTMLElement;
    const handle = document.querySelector('.drag-handle') as HTMLElement;

    if (popup && handle) {
      this.makeLanguageDraggable(popup, handle);
      console.log('Language popup drag functionality initialized');
    }
  }

  // Make element draggable - Exact Copy from Query Popup
  private makeLanguageDraggable(element: HTMLElement, handle: HTMLElement): void {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let elementX = 0;
    let elementY = 0;

    // Get initial position
    const rect = element.getBoundingClientRect();
    elementX = rect.left;
    elementY = rect.top;

    const dragStart = (e: MouseEvent) => {
      // Only start drag if clicking on the header (not buttons)
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        return;
      }

      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;

      // Get current position
      const currentRect = element.getBoundingClientRect();
      elementX = currentRect.left;
      elementY = currentRect.top;

      // Visual feedback - only change header cursor
      handle.style.cursor = 'grabbing';
      element.classList.add('dragging');

      // Prevent text selection
      e.preventDefault();
      document.body.style.userSelect = 'none';
    };

    const dragEnd = () => {
      if (!isDragging) return;

      isDragging = false;
      handle.style.cursor = 'grab';
      element.classList.remove('dragging');
      document.body.style.userSelect = '';
    };

    const drag = (e: MouseEvent) => {
      if (!isDragging) return;

      e.preventDefault();

      // Calculate new position
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newX = elementX + deltaX;
      let newY = elementY + deltaY;

      // Constrain to viewport with some padding
      const padding = 20;
      const maxX = window.innerWidth - element.offsetWidth - padding;
      const maxY = window.innerHeight - element.offsetHeight - padding;

      newX = Math.max(padding, Math.min(newX, maxX));
      newY = Math.max(padding, Math.min(newY, maxY));

      // Apply position using left/top instead of transform
      element.style.left = `${newX}px`;
      element.style.top = `${newY}px`;
      element.style.transform = 'none';
      element.style.position = 'fixed';
    };

    // Add event listeners
    handle.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);

    // Store cleanup function
    (element as any).dragCleanup = () => {
      handle.removeEventListener('mousedown', dragStart);
      document.removeEventListener('mousemove', drag);
      document.removeEventListener('mouseup', dragEnd);
    };
  }

  // Center popup functionality - Match Query Popup
  centerPopup(): void {
    const popup = document.querySelector('.language-popup') as HTMLElement;
    if (!popup) return;

    // Get current popup dimensions
    const rect = popup.getBoundingClientRect();
    const popupWidth = rect.width;
    const popupHeight = rect.height;

    // Calculate new center position
    const centerX = Math.max(20, (window.innerWidth - popupWidth) / 2);
    const centerY = Math.max(20, (window.innerHeight - popupHeight) / 2);

    // Apply new position
    popup.style.left = `${centerX}px`;
    popup.style.top = `${centerY}px`;
    popup.style.transform = 'none';
    popup.style.position = 'fixed';

    console.log('Language popup recentered to:', { centerX, centerY, popupWidth, popupHeight });
  }

  // Helper methods for enhanced functionality
  getLanguageFlag(language: string): string {
    const flags: { [key: string]: string } = {
      'en': '🇺🇸',
      'ar': '🇸🇦',
      'fr': '🇫🇷',
      'es': '🇪🇸',
      'de': '🇩🇪',
      'it': '🇮🇹',
      'pt': '🇵🇹',
      'ru': '🇷🇺',
      'zh': '🇨🇳',
      'ja': '🇯🇵',
      'ko': '🇰🇷'
    };
    return flags[language] || '🌐';
  }

  getCharacterCount(language: string): number {
    const control = this.languageControls[language];
    return control?.value?.length || 0;
  }

  getModifiedLanguagesCount(): number {
    return Object.keys(this.languageControls).filter(lang => {
      const control = this.languageControls[lang];
      return control?.value && control.value.trim().length > 0;
    }).length;
  }

  clearAllLanguages(): void {
    Object.keys(this.languageControls).forEach(lang => {
      this.languageControls[lang].setValue('');
    });
  }

  onInputFocus(event: Event): void {
    const input = event.target as HTMLElement;
    input.parentElement?.classList.add('focused');
  }

  onInputBlur(event: Event): void {
    const input = event.target as HTMLElement;
    input.parentElement?.classList.remove('focused');
  }
}
