import { Component, OnInit,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
  ChangeDetectorRef,
  EnvironmentInjector,
  ElementRef,
  AfterViewInit,
  inject } from '@angular/core';
import { MatSidenavModule, MatSidenav } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MetadataService } from '../services/metadata.service';
import { AuthenticationService } from '../services/authentication.service';
import { SessionStorageService } from '../services/session-storage.service';
import { NavigationService } from '../services/navigation.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SubmenuComponent } from '../submenu/submenu.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';
import { DynamicQueryComponent } from '../dynamic-query/dynamic-query.component';
import { KeycloakService } from '../services/keycloak.service';
import { SidebarSearchComponent } from '../search/sidebar-search/sidebar-search.component';

@Component({
  selector: 'app-home',
  imports: [MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    SubmenuComponent,
    MatSidenav,
    SidebarSearchComponent],
     animations: [
    trigger('tabAnimation', [
      transition(':increment', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateX(20px)' }),
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
          ])
        ], { optional: true })
      ]),
      transition(':decrement', [
        query(':leave', [
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 0, transform: 'translateX(-20px)' }))
          ])
        ], { optional: true })
      ])
    ])
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit, AfterViewInit {
  tabs: any[] = [];
  sidebarMenus: { [key: string]: any[] } = {};
  tabMenus: { [key: string]: any[] } = {};
  loadingSidebarMenus: { [key: string]: boolean } = {};
  loadingTabMenus: { [key: string]: boolean } = {};
  expandedSidebarMenus: { [key: string]: boolean } = {};
  expandedTabMenus: { [key: string]: { [key: string]: boolean } } = {};

  // Sidebar filtering for search results
  sidebarFilterActive: boolean = false;
  filteredTabs: any[] = [];
  filteredSubmenus: { [key: string]: any[] } = {};
  selectedSearchItem: any = null;

  openTabs: any[] = [];
  activeTabIndex: number = -1;

  @ViewChild('sidenav') sidenav!: MatSidenav;
  isSidenavOpen: boolean = false;

  @ViewChild('tabContent', { read: ViewContainerRef }) tabContent!: ViewContainerRef;
  @ViewChild('tabScrollWrapper') tabScrollWrapper!: ElementRef;
  @ViewChild('tabsList') tabsList!: ElementRef;

  // Tab scrolling properties
  canScrollLeft: boolean = false;
  canScrollRight: boolean = false;


   userPrivileges: any[] = [];

  componentRefs: { [key: number]: ComponentRef<any> } = {}; // Store component references
  private environmentInjector = inject(EnvironmentInjector);

constructor(private metadataService: MetadataService,
    private authenticationService: AuthenticationService,
    private sessionStorage: SessionStorageService,
    private navigationService: NavigationService,
    private router: Router,
    private location: Location,
    private changeDetectorRef: ChangeDetectorRef,
    private keycloakService: KeycloakService,
){}
//  ngOnInit() {
//     const profile = JSON.parse(localStorage.getItem('profile') || '{}');
//     this.tabs = profile.menus || [];
//     console.log('Main Menu Loaded:', this.tabs);

//     this.location.subscribe(() => {
//       this.onLogout();
//     });
//   }

ngOnInit() {
  const profile = this.sessionStorage.getUserProfile() || {};
  this.tabs = profile.menus || [];

  this.userPrivileges = this.sessionStorage.getUserPrivileges();

  this.location.subscribe(() => {
    this.onLogout();
  });
}

selectBranch(branch: any): void {
  this.sessionStorage.setSelectedBranch(branch);
  window.location.reload();
}

  ngAfterViewInit() {
    // Check scroll state after view initialization
    setTimeout(() => {
      this.checkScrollState();
    }, 100);

    // Add resize observer to update scroll state when container size changes
    if (this.tabScrollWrapper) {
      const resizeObserver = new ResizeObserver(() => {
        this.checkScrollState();
      });
      resizeObserver.observe(this.tabScrollWrapper.nativeElement);
    }
  }

   onSidebarMenuItemSelected(menuItem: any) {
    if (menuItem.type === 'menu') {
      this.expandedSidebarMenus[menuItem.application] = !this.expandedSidebarMenus[menuItem.application];
      if (!this.sidebarMenus[menuItem.application]) {
        this.loadSidebarSubmenu(menuItem.application);
      }
    } else {
      this.openTab(menuItem);
    }

    // Reinitialize or reorganize layout
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 100); // Delay of 100ms
  }

  /**
   * Handle menu selection from global search
   */
  async onSearchMenuSelected(menuItem: any) {
    console.log('Home Component - Received search menu selection:', menuItem);

    // Filter sidebar to show only the path to this item
    await this.filterSidebarForItem(menuItem);

    // Open the selected item
    this.onSidebarMenuItemSelected(menuItem);
  }

  /**
   * Filter sidebar to show only the path to the selected item
   */
  private async filterSidebarForItem(menuItem: any) {
    console.log('Filtering sidebar for item:', menuItem);

    // Step 1: Always open sidebar to show the filter
    if (!this.isSidenavOpen) {
      this.toggleSideMenu();
    }

    // Step 2: Set filter active
    this.sidebarFilterActive = true;
    this.selectedSearchItem = menuItem;

    // Step 3: Create filtered view
    await this.createFilteredSidebar(menuItem);

    // Step 4: Wait for sidebar to open, then force UI update
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 300);
  }

  /**
   * Create filtered sidebar showing only the path to the item
   */
  private async createFilteredSidebar(menuItem: any) {
    console.log('=== FILTERING SIDEBAR ===');
    console.log('Target item:', {
      application: menuItem.application,
      type: menuItem.type,
      description: menuItem.description,
      hasComma: menuItem.application?.includes(',')
    });

    this.filteredTabs = [];
    this.filteredSubmenus = {};

    // Find the parent menu for this item
    const parentMenu = await this.findParentMenuForItem(menuItem);
    console.log('Found parent menu:', parentMenu);

    if (parentMenu) {
      // Show only the parent menu
      this.filteredTabs = [parentMenu];

      // Expand the parent menu
      this.expandedSidebarMenus[parentMenu.application] = true;

      // Load submenu if needed
      if (!this.sidebarMenus[parentMenu.application]) {
        this.loadSidebarSubmenu(parentMenu.application);
      }

      // Filter submenu to show only the selected item (exact match by application AND type)
      setTimeout(() => {
        const allSubmenus = this.sidebarMenus[parentMenu.application] || [];
        console.log('All submenus in parent:', allSubmenus);
        console.log('Looking for exact match of:', menuItem);

        this.filteredSubmenus[parentMenu.application] = allSubmenus.filter(submenu => {
          const matches = this.isExactMatch(submenu, menuItem);
          return matches;
        });

        console.log('Filtered submenus result:', this.filteredSubmenus[parentMenu.application]);
        this.changeDetectorRef.detectChanges();
      }, 300);

    } else {
      // If no parent found, this is a main menu item - use exact match
      console.log('No parent found, filtering main tabs with exact match for:', menuItem);
      this.filteredTabs = this.tabs.filter(tab => {
        const matches = this.isExactMatch(tab, menuItem);
        console.log('Main tab check:', tab, 'matches:', matches);
        return matches;
      });
    }

    console.log('Filtered tabs:', this.filteredTabs);
    console.log('Filtered submenus:', this.filteredSubmenus);
  }

  /**
   * Find parent menu for the given item
   */
  private async findParentMenuForItem(menuItem: any): Promise<any> {
    console.log('Finding parent for:', menuItem);

    // Check if item has explicit parent
    if (menuItem.parent) {
      const parent = this.tabs.find(tab => tab.application === menuItem.parent);
      console.log('Found explicit parent:', parent);
      return parent;
    }

    // Dynamic parent detection - search through all menu items to find the actual parent
    console.log('Searching for parent dynamically...');

    // First, search through all loaded menus to find which one contains this item
    for (const tab of this.tabs) {
      if (tab.type === 'menu' && this.sidebarMenus[tab.application]) {
        const containsItem = this.sidebarMenus[tab.application].some((submenu: any) =>
          this.isExactMatch(submenu, menuItem)
        );
        if (containsItem) {
          console.log('Found parent by direct search:', tab);
          return tab;
        }
      }
    }

    // If not found in loaded menus, search through ALL menu tabs and load them to check
    // This is truly dynamic - no assumptions about naming patterns
    console.log('Searching through all menu tabs to find the actual parent...');

    for (const tab of this.tabs) {
      // Only check menu type tabs (potential parents)
      if (tab.type === 'menu') {
        console.log('Checking menu tab:', tab.application);

        // Load the menu if not loaded
        if (!this.sidebarMenus[tab.application]) {
          console.log('Loading menu to check:', tab.application);
          try {
            await this.loadSidebarSubmenuAsync(tab.application);
          } catch (error) {
            console.log('Failed to load menu:', tab.application, error);
            continue; // Skip this menu and try the next one
          }
        }

        // Now verify if it contains the item
        if (this.verifyParentContainsItem(tab, menuItem)) {
          console.log('Found actual parent by loading and verifying:', tab);
          return tab;
        }
      }
    }

    // If no parent found by patterns, search through all menus to find which one contains this item
    console.log('Searching through all loaded menus...');
    for (const tab of this.tabs) {
      if (tab.type === 'menu' && this.sidebarMenus[tab.application]) {
        const containsItem = this.sidebarMenus[tab.application].some((submenu: any) =>
          this.isExactMatch(submenu, menuItem)
        );
        if (containsItem) {
          console.log('Found parent by searching:', tab);
          return tab;
        }
      }
    }

    // Final check: is this item actually in the main menu?
    const isInMainMenu = this.tabs.some(tab => this.isExactMatch(tab, menuItem));
    console.log('Item in main menu check:', isInMainMenu);

    if (isInMainMenu) {
      console.log('Item is in main menu, no parent needed');
      return null;
    }

    // If not in main menu and no parent found, it might be in an unloaded submenu
    console.log('Item not found in main menu or loaded submenus - might be in unloaded submenu');
    return null;
  }

  /**
   * Verify that a parent menu actually contains the specified item
   */
  private verifyParentContainsItem(parentTab: any, menuItem: any): boolean {
    if (!this.sidebarMenus[parentTab.application]) {
      // Menu not loaded yet, we need to load it first to verify
      console.log('Menu not loaded yet, loading to verify:', parentTab.application);
      this.loadSidebarSubmenu(parentTab.application);
      // For now, return false since we can't verify without loading
      // The search will try other parents or fall back to main menu
      return false;
    }

    const containsItem = this.sidebarMenus[parentTab.application].some((submenu: any) =>
      this.isExactMatch(submenu, menuItem)
    );

    console.log('Parent verification:', {
      parent: parentTab.application,
      containsItem,
      targetItem: { app: menuItem.application, type: menuItem.type },
      submenus: this.sidebarMenus[parentTab.application].map((s: any) => ({ app: s.application, type: s.type }))
    });

    return containsItem;
  }

  /**
   * Check if two menu items are an exact match (application + type)
   */
  private isExactMatch(item1: any, item2: any): boolean {
    // Exact application match
    const applicationMatch = item1.application === item2.application;

    // Exact type match
    const typeMatch = item1.type === item2.type;

    console.log('Exact match check:', {
      item1: { app: item1.application, type: item1.type },
      item2: { app: item2.application, type: item2.type },
      applicationMatch,
      typeMatch,
      result: applicationMatch && typeMatch
    });

    return applicationMatch && typeMatch;
  }

  /**
   * Distinguish between different item types based on application pattern and type
   */
  private getItemSignature(item: any): string {
    // Create a unique signature for the item
    const hasComma = item.application && item.application.includes(',');
    return `${item.application}|${item.type}|${hasComma ? 'comma' : 'simple'}`;
  }

  /**
   * Load sidebar submenu asynchronously and return a promise
   */
  private loadSidebarSubmenuAsync(application: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.sidebarMenus[application]) {
        resolve();
        return;
      }

      this.loadingSidebarMenus[application] = true;

      // Use extracted API ID for API calls
      const apiId = this.extractApiId(application);

      this.metadataService.getMenu(apiId).subscribe({
        next: (response: any) => {
          this.sidebarMenus[application] = response?.menus || [];
          this.loadingSidebarMenus[application] = false;
          console.log('Loaded submenu async:', application, this.sidebarMenus[application]);
          resolve();
        },
        error: (error: any) => {
          this.loadingSidebarMenus[application] = false;
          console.error('Failed to load submenu:', application, error);
          reject(error);
        }
      });
    });
  }

  /**
   * Clear sidebar filter and show all items
   */
  clearSidebarFilter() {
    this.sidebarFilterActive = false;
    this.filteredTabs = [];
    this.filteredSubmenus = {};
    this.selectedSearchItem = null;
    this.changeDetectorRef.detectChanges();
  }

  /**
   * Get tabs to display (filtered or all)
   */
  getDisplayTabs(): any[] {
    return this.sidebarFilterActive ? this.filteredTabs : this.tabs;
  }

  /**
   * Get submenus to display (filtered or all)
   */
  getDisplaySubmenus(application: string): any[] {
    if (this.sidebarFilterActive && this.filteredSubmenus[application]) {
      return this.filteredSubmenus[application];
    }
    return this.sidebarMenus[application] || [];
  }
  

  openTab(menuItem: any) {
    let existingTabIndex = -1;
    const isQuery = menuItem.type === 'qur';

    if (isQuery) {
      existingTabIndex = this.openTabs.findIndex(t => t.type === 'qur');
    } else {
      existingTabIndex = this.openTabs.findIndex(
        (tab) => tab.application === menuItem.application && tab.type === menuItem.type
      );
    }

    if (existingTabIndex > -1) {
      // Tab exists
      this.activeTabIndex = existingTabIndex;
      const existingTab = this.openTabs[existingTabIndex];

      if (isQuery && existingTab.application !== menuItem.application) {
        // It's a different query, so we need to update the component
        // Destroy the old component
        if (this.componentRefs[existingTab.id]) {
          this.componentRefs[existingTab.id].destroy();
          delete this.componentRefs[existingTab.id];
        }
        // Update the tab data
        this.openTabs[existingTabIndex] = { ...menuItem, id: existingTab.id, data: {}, formState: null };
      }
    } else {
      // Create a new tab
      const newTab = { ...menuItem, id: Date.now(), data: {}, formState: null };
      this.openTabs.push(newTab);
      this.activeTabIndex = this.openTabs.length - 1;
    }

    // Load component for the active tab.
    // This will create it if it doesn't exist.
    if (this.activeTabIndex !== -1) {
      this.loadComponent(this.openTabs[this.activeTabIndex]);
    }

    // Check scroll state after tab changes
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }
  
  loadComponent(tab: any) {
    // Ensure all other components are hidden before proceeding
    this.hideAllComponents();

    if (this.componentRefs[tab.id]) {
      // If the component already exists, show it
      this.showComponent(tab.id);
    } else {
      // Create the component and store its reference
      this.createComponent(tab);
    }
  
    // Trigger change detection to ensure the view updates
    this.changeDetectorRef.detectChanges();
  }

  // Helper function to extract API ID from application string
  private extractApiId(application: string): string {
    if (application && application.includes(',')) {
      return application.split(',')[0].trim();
    }
    return application;
  }

  createComponent(tab: any) {
    let componentRef: ComponentRef<any>;
    
    // Both 'table' and 'scr' types now use DynamicFormComponent
    switch (tab.type) {
      case 'table':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.tableName = tab.application;
        break;
      case 'scr':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.screenName = tab.application; // Use screenName for screen types
        break;
      case 'qur':
        componentRef = this.tabContent.createComponent(DynamicQueryComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.queryName = tab.application;
        break;
      default:
        return;
    }

    // Store the component reference and initialize data
    this.componentRefs[tab.id] = componentRef;
    componentRef.instance.data = tab.data;

    // Listen for data changes to keep the state updated (only if the component has dataChange)
    if (componentRef.instance.dataChange) {
      componentRef.instance.dataChange.subscribe((updatedData: any) => {
        tab.data = updatedData;
      });
    }

    // Hide all other components and show the new one
    this.showComponent(tab.id);
  }


  hideAllComponents() {
    for (const key in this.componentRefs) {
      if (this.componentRefs.hasOwnProperty(key)) {
        this.componentRefs[key].location.nativeElement.style.display = 'none';
      }
    }
  }

  showComponent(tabId: number) {
    // First, hide all components
    this.hideAllComponents();
  
    // Then, show the active one
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].location.nativeElement.style.display = 'block';
    }
  }
  

  setActiveTab(index: number) {
    this.activeTabIndex = index;
    this.showComponent(this.openTabs[index].id);
  }
  

  closeTab(index: number) {
    const tabId = this.openTabs[index].id;
    this.openTabs.splice(index, 1);

    // Destroy the component and remove its reference
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].destroy();
      delete this.componentRefs[tabId];
    }

    if (this.openTabs.length === 0) {
      this.activeTabIndex = -1;
    } else if (this.activeTabIndex >= this.openTabs.length) {
      this.activeTabIndex = this.openTabs.length - 1;
    }

    if (this.activeTabIndex !== -1) {
      this.showComponent(this.openTabs[this.activeTabIndex].id);
    }

    // Check scroll state after tab removal
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }

  loadSidebarSubmenu(application: string) {
    this.loadingSidebarMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.sidebarMenus[application] = response?.menus || [];
        this.loadingSidebarMenus[application] = false;
      },
      error: (error: any) => {
        this.loadingSidebarMenus[application] = false;
      }
    });
  }

  loadTabSubmenu(application: string) {
    this.loadingTabMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.tabMenus[application] = response?.menus || [];
        this.loadingTabMenus[application] = false;
      },
      error: (error: any) => {
        this.loadingTabMenus[application] = false;
      }
    });
  }

  toggleSideMenu() {
    this.sidenav.toggle();
    this.isSidenavOpen = !this.isSidenavOpen;
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }

  // Tab scrolling methods
  checkScrollState() {
    if (this.tabScrollWrapper && this.tabsList) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const list = this.tabsList.nativeElement;

      this.canScrollLeft = wrapper.scrollLeft > 0;
      this.canScrollRight = wrapper.scrollLeft < (list.scrollWidth - wrapper.clientWidth);

      // Force change detection to update button states
      this.changeDetectorRef.detectChanges();
    } else {
    }
  }

  scrollTabsLeft() {
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      wrapper.scrollTo({
        left: wrapper.scrollLeft - scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
    }
  }

  scrollTabsRight() {
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      wrapper.scrollTo({
        left: wrapper.scrollLeft + scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
    }
  }

  // Add scroll event listener to update arrow states
  onTabScroll() {
    this.checkScrollState();
  }

  onLogout() {
    this.sessionStorage.clear();
    this.keycloakService.logout(); // Only Keycloak logout for SSO
  }

}
