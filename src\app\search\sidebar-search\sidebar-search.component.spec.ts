import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SidebarSearchComponent } from './sidebar-search.component';
import { SidebarSearchService } from '../../services/sidebar-search.service';
import { of } from 'rxjs';
import { SearchResult, SearchResultType } from '../../core/models/search-result';

describe('SidebarSearchComponent', () => {
  let component: SidebarSearchComponent;
  let fixture: ComponentFixture<SidebarSearchComponent>;
  let mockSearchService: jasmine.SpyObj<SidebarSearchService>;

  const mockSearchResults: SearchResult[] = [
    {
      id: 'test-1',
      title: 'Test Menu',
      description: 'Test menu description',
      type: SearchResultType.MENU,
      category: 'Navigation',
      icon: 'menu',
      score: 100,
      metadata: { application: 'test', type: 'menu' }
    },
    {
      id: 'test-2',
      title: 'Test Form',
      description: 'Test form description',
      type: SearchResultType.FORM,
      category: 'Forms',
      icon: 'description',
      score: 90,
      metadata: { application: 'testform', type: 'form' }
    }
  ];

  beforeEach(async () => {
    const searchServiceSpy = jasmine.createSpyObj('SidebarSearchService', [
      'getSearchObservable',
      'updateSearchQuery',
      'isIndexReady',
      'getSearchCategories'
    ]);

    await TestBed.configureTestingModule({
      imports: [SidebarSearchComponent, NoopAnimationsModule],
      providers: [
        { provide: SidebarSearchService, useValue: searchServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SidebarSearchComponent);
    component = fixture.componentInstance;
    mockSearchService = TestBed.inject(SidebarSearchService) as jasmine.SpyObj<SidebarSearchService>;

    // Setup default mock returns
    mockSearchService.getSearchObservable.and.returnValue(of(mockSearchResults));
    mockSearchService.isIndexReady.and.returnValue(of(true));
    mockSearchService.getSearchCategories.and.returnValue([]);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty search query', () => {
    expect(component.searchQuery).toBe('');
    expect(component.showResults).toBeFalse();
    expect(component.selectedIndex).toBe(-1);
  });

  it('should update search query on input', () => {
    component.searchQuery = 'test query';
    component.onSearchInput();
    
    expect(mockSearchService.updateSearchQuery).toHaveBeenCalledWith('test query');
    expect(component.isSearching).toBeTrue();
  });

  it('should not search for queries less than 2 characters', () => {
    component.searchQuery = 'a';
    component.onSearchInput();
    
    expect(mockSearchService.updateSearchQuery).not.toHaveBeenCalled();
    expect(component.showResults).toBeFalse();
  });

  it('should handle keyboard navigation', () => {
    component.searchResults = mockSearchResults;
    component.showResults = true;
    component.selectedIndex = -1;

    // Test arrow down
    const downEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
    spyOn(downEvent, 'preventDefault');
    component.onKeyDown(downEvent);

    expect(downEvent.preventDefault).toHaveBeenCalled();
    expect(component.selectedIndex).toBe(0);

    // Test arrow up
    const upEvent = new KeyboardEvent('keydown', { key: 'ArrowUp' });
    spyOn(upEvent, 'preventDefault');
    component.onKeyDown(upEvent);

    expect(upEvent.preventDefault).toHaveBeenCalled();
    expect(component.selectedIndex).toBe(1); // Should wrap to last item
  });

  it('should emit menu selection event', () => {
    spyOn(component.menuSelected, 'emit');
    component.searchResults = mockSearchResults;
    
    component.selectResult(0);
    
    expect(component.menuSelected.emit).toHaveBeenCalledWith(mockSearchResults[0].metadata);
  });

  it('should clear results', () => {
    component.searchResults = mockSearchResults;
    component.showResults = true;
    component.selectedIndex = 1;
    component.isSearching = true;

    component.clearResults();

    expect(component.showResults).toBeFalse();
    expect(component.searchResults).toEqual([]);
    expect(component.selectedIndex).toBe(-1);
    expect(component.isSearching).toBeFalse();
  });

  it('should handle visible results correctly', () => {
    const manyResults = Array(15).fill(null).map((_, i) => ({
      ...mockSearchResults[0],
      id: `test-${i}`,
      title: `Test ${i}`
    }));
    
    component.searchResults = manyResults;
    
    const visibleResults = component.getVisibleResults();
    expect(visibleResults.length).toBe(component.maxVisibleResults);
    expect(component.hasMoreResults()).toBeTrue();
    expect(component.getMoreResultsCount()).toBe(manyResults.length - component.maxVisibleResults);
  });

  it('should focus search input', () => {
    spyOn(component.searchInput.nativeElement, 'focus');
    component.focusSearch();
    expect(component.searchInput.nativeElement.focus).toHaveBeenCalled();
  });

  it('should clear search', () => {
    component.searchQuery = 'test';
    component.searchResults = mockSearchResults;
    component.showResults = true;

    component.clearSearch();

    expect(component.searchQuery).toBe('');
    expect(component.showResults).toBeFalse();
    expect(component.searchResults).toEqual([]);
  });
});
