<!-- Multi-Column Dropdown Component Template -->
<div class="multi-column-dropdown-container" [class.disabled]="isDisabled || isReadonly" [class]="containerClass">
  
  <!-- Input field with search capability -->
  <input 
    [id]="uniqueId"
    [formControl]="formControl"
    [class]="inputClass"
    [placeholder]="placeholderText"
    [disabled]="isDisabled"
    [readonly]="isReadonly"
    type="text"
    autocomplete="off"
    (input)="onInputChange($event)"
    (focus)="onInputFocus()"
    (blur)="onInputBlur()" />
  
  <!-- Arrow button to toggle dropdown -->
  <button 
    type="button" 
    class="dropdown-arrow-btn" 
    (click)="toggleDropdown()" 
    [disabled]="isDisabled || isReadonly"
    [matTooltip]="tooltipText">
    <mat-icon>{{ dropdownArrowIcon }}</mat-icon>
  </button>
  
  <!-- Dropdown content -->
  @if (showDropdown) {
    <div class="dropdown-content" [style.max-height]="dropdownMaxHeight">
      
      <!-- Loading state -->
      @if (isLoading) {
        <div class="dropdown-loading">
          <mat-icon class="loading-icon">refresh</mat-icon>
          Loading...
        </div>
      }
      
      <!-- Multi-column table view -->
      @else if (filteredOptions && filteredOptions.length > 0 && hasMultipleColumns) {
        <div class="dropdown-table-container">
          <table class="dropdown-table">
            
            <!-- Table headers -->
            @if (showHeaders) {
              <thead>
                <tr>
                  @for (column of columns; track trackByColumn($index, column)) {
                    <th class="table-header">{{ column.label }}</th>
                  }
                </tr>
              </thead>
            }
            
            <!-- Table body -->
            <tbody>
              @for (option of filteredOptions; track trackByOption($index, option)) {
                <tr class="table-row" (click)="selectOption(option)">
                  @for (column of columns; track trackByColumn($index, column)) {
                    <td class="table-cell">
                      {{ option[column.fieldName] || '' }}
                    </td>
                  }
                </tr>
              }
            </tbody>

          </table>

          <!-- View List Button at bottom of table -->
          @if (showViewListButton) {
            <div class="view-list-footer">
              <button
                type="button"
                class="view-list-footer-btn"
                (click)="openDynamicQuery()"
                [matTooltip]="'View all ' + allColumns.length + ' columns with advanced search'">
                <mat-icon>view_list</mat-icon>
                <span>View List ({{allColumns.length}} columns)</span>
              </button>
            </div>
          }
        </div>
      }
      
      <!-- Single column list view (fallback) -->
      @else if (filteredOptions && filteredOptions.length > 0) {
        <div class="dropdown-list">
          @for (option of filteredOptions; track trackByOption($index, option)) {
            <div class="dropdown-item" (click)="selectOption(option)">
              {{ getOptionDisplayText(option) }}
            </div>
          }
        </div>
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty">
          {{ emptyMessage }}
        </div>
      }
      
    </div>
  }
  
</div>
