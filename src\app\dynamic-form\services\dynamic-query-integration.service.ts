import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

export interface DynamicQueryIntegrationConfig {
  queryBuilderId: string;
  title?: string;
  description?: string;
  data?: any[];
  columns?: any[];
}

export interface DynamicQueryIntegrationEvent {
  type: 'OPEN_QUERY' | 'CLOSE_QUERY' | 'ITEM_SELECTED' | 'QUERY_EXECUTED';
  config?: DynamicQueryIntegrationConfig;
  selectedItem?: any;
  queryResults?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class DynamicQueryIntegrationService {
  
  private events$ = new Subject<DynamicQueryIntegrationEvent>();

  constructor() {}

  /**
   * Get observable for integration events
   */
  getEvents(): Observable<DynamicQueryIntegrationEvent> {
    return this.events$.asObservable();
  }

  /**
   * Emit an integration event
   */
  emitEvent(event: DynamicQueryIntegrationEvent): void {
    console.log('Dynamic Query Integration Event:', event);
    this.events$.next(event);
  }

  /**
   * Open dynamic query with configuration
   */
  openDynamicQuery(config: DynamicQueryIntegrationConfig): void {
    this.emitEvent({
      type: 'OPEN_QUERY',
      config: config
    });
  }

  /**
   * Close dynamic query
   */
  closeDynamicQuery(): void {
    this.emitEvent({
      type: 'CLOSE_QUERY'
    });
  }

  /**
   * Handle item selection from dynamic query
   */
  selectItem(item: any): void {
    this.emitEvent({
      type: 'ITEM_SELECTED',
      selectedItem: item
    });
  }

  /**
   * Handle query execution results
   */
  handleQueryResults(results: any[]): void {
    this.emitEvent({
      type: 'QUERY_EXECUTED',
      queryResults: results
    });
  }
}
