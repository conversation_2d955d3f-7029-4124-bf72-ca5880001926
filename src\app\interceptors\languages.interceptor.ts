import { HttpInterceptorFn } from '@angular/common/http';

export const languagesInterceptor: HttpInterceptorFn = (req, next) => {
  // Get user languages from sessionStorage
  const userLanguages = getUserLanguagesFromStorage();

  // Only add languages header if languages exist
  if (userLanguages && userLanguages.length > 0) {
    const modifiedReq = req.clone({
      setHeaders: {
        'Accept-Language': userLanguages.join(','),
        'X-User-Languages': JSON.stringify(userLanguages)
      }
    });
    return next(modifiedReq);
  }

  return next(req);
};

/**
 * Helper function to get user languages from sessionStorage
 */
function getUserLanguagesFromStorage(): string[] {
  try {
    const languages = sessionStorage.getItem('userLanguages');
    return languages ? JSON.parse(languages) : [];
  } catch (error) {
    return [];
  }
}
