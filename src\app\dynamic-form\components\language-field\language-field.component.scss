/* Language Field Component - EXACT match to existing form field styles */

/* Import existing form field styles - EXACT from regular-field.component.scss */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

/* Required field indicator styling - EXACT match */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling - EXACT match */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Form Input - EXACT match to existing styles */
.form-input:not([type='checkbox']) {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 8px 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/* Disabled/readonly styles - EXACT match */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Language-specific styles */
.language-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.language-main-input {
  padding-right: 45px !important; /* Make room for globe icon */
}

.language-globe-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: #2196f3;
  }

  &:focus {
    outline: none;
    background-color: rgba(33, 150, 243, 0.1);
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Language Popup Styles - Match Query Popup */
.language-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
  z-index: 1999;
  pointer-events: none;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-in-out;
}

.language-popup {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 95vw;
  max-height: 90vh;
  width: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2001;
  cursor: default;
  border: 2px solid #3b82f6;
  pointer-events: auto;
  animation: slideIn 0.3s ease-out;
}

/* Prevent text selection during drag - Match Query Popup */
.language-popup.dragging {
  user-select: none !important;
  box-shadow: 0 35px 70px -12px rgba(0, 0, 0, 0.4) !important;
  transform: scale(1.02) !important;
  transition: none !important;
  position: fixed !important;
}

.language-popup.dragging * {
  user-select: none !important;
  pointer-events: none !important;
}

.language-popup.dragging .drag-handle {
  pointer-events: auto !important;
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
}

.language-popup.dragging .drag-handle button {
  pointer-events: auto !important;
}

/* Smooth transitions when not dragging */
.language-popup:not(.dragging) {
  transition: all 0.2s ease !important;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  cursor: grab;
  user-select: none;

  &:active {
    cursor: grabbing;
  }

  &:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }

  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
  }

  p {
    margin: 2px 0 0 0;
    opacity: 0.9;
    font-size: 12px;
    color: white;
  }

  .center-btn,
  .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    opacity: 0.8;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      opacity: 1;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.popup-content {
  padding: 24px;
  max-height: 50vh;
  overflow-y: auto;
  background: #fafbfc;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;

    &:hover {
      background: #94a3b8;
    }
  }
}

.popup-field {
  margin-bottom: 28px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
  }

  &.default-field {
    border: 2px solid #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);

    .popup-field-label {
      .language-name {
        font-weight: 600;
        color: #1d4ed8;
      }

      .default-indicator {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  /* Field Animation */
  animation: fieldSlideIn 0.4s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }

  /* Loading State */
  &.loading {
    .popup-input {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
    }
  }

  /* Success State */
  &.success {
    .popup-input {
      border-color: #10b981;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
  }

  /* Error State */
  &.error {
    .popup-input {
      border-color: #ef4444;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }

  /* Cleared State - Visual Feedback */
  &.cleared {
    animation: clearPulse 1s ease-out;

    .popup-input {
      border-color: #f59e0b;
      box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
      background: #fffbeb;
    }
  }
}

.popup-field-label {
  display: block;
  margin-bottom: 16px;
  font-weight: 500;
  font-size: 14px;
  color: #333;

  .language-label-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
  }

  .language-flag {
    font-size: 24px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  .language-name {
    font-weight: 600;
    color: #374151;
    font-size: 16px;
  }

  .default-indicator {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
    background: #f1f5f9;
    padding: 3px 8px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
  }
}

.popup-input-container {
  position: relative;
  margin-bottom: 8px;

  &.focused {
    .popup-input {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }
  }
}

.popup-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  line-height: 1.5;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    background: #fefefe;
  }

  &:hover:not(:disabled):not([readonly]) {
    border-color: #94a3b8;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  }

  &::placeholder {
    color: #94a3b8;
    font-style: italic;
    font-weight: 400;
  }

  &.professional-input {
    font-weight: 400;
    min-height: 56px;
  }

  &.professional-textarea {
    font-weight: 400;
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
    font-family: 'Poppins', sans-serif;
  }

  &:disabled,
  &[readonly] {
    background: #f8fafc;
    border-color: #e2e8f0;
    color: #64748b;
    cursor: not-allowed;
  }
}

.character-count {
  position: absolute;
  bottom: -24px;
  right: 4px;
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  background: white;
  padding: 2px 8px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;

  .footer-info {
    color: #64748b;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: "📊";
      font-size: 16px;
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .clear-btn,
  .cancel-btn,
  .save-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
    position: relative;
    overflow: hidden;

    mat-icon {
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }

  .clear-btn {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 2px solid #f59e0b;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
      color: #78350f;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
      border-color: #f59e0b;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(245, 158, 11, 0.2);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
    }

    mat-icon {
      color: #f59e0b;
      transition: all 0.2s ease;
    }

    &:hover mat-icon {
      color: #d97706;
      transform: scale(1.1);
    }
  }

  .cancel-btn {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    border: 2px solid #fecaca;

    &:hover {
      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
      color: #b91c1c;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
      border-color: #f87171;
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(220, 38, 38, 0.15);
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: 2px solid #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
      border-color: #2563eb;
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  .save-btn,
  .cancel-btn,
  .clear-btn {
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fieldSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes clearPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-input:not([type='checkbox']) {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }

  .language-main-input {
    padding-right: 40px !important;
  }

  .language-globe-btn {
    right: 6px;
    width: 28px;
    height: 28px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .language-popup {
    width: 95%;
    max-height: 90vh;
  }

  .popup-header,
  .popup-footer {
    padding: 16px 20px;
  }

  .popup-content {
    padding: 20px;
  }

  .popup-footer {
    flex-direction: column;
    gap: 8px;

    .save-btn,
    .cancel-btn {
      width: 100%;
      padding: 12px 20px;
    }
  }
}

@media (max-width: 480px) {
  .form-input:not([type='checkbox']) {
    padding: 8px 10px;
    font-size: 14px;
  }

  .language-main-input {
    padding-right: 35px !important;
  }

  .language-globe-btn {
    right: 4px;
    width: 24px;
    height: 24px;

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }

  .popup-header h4 {
    font-size: 16px;
  }

  .popup-field-label {
    font-size: 13px;
  }

  .popup-input {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* No Input Indicator Responsive - EXACT match */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* Animation for language switching */
.language-input {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
