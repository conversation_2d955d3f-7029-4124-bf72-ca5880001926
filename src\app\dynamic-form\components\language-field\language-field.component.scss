/* Language Field Component - EXACT match to existing form field styles */

/* Import existing form field styles - EXACT from regular-field.component.scss */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

/* Required field indicator styling - EXACT match */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling - EXACT match */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Form Input - EXACT match to existing styles */
.form-input:not([type='checkbox']) {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 8px 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

/* Disabled/readonly styles - EXACT match */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Language-specific styles */
.language-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.language-main-input {
  padding-right: 45px !important; /* Make room for globe icon */
}

.language-globe-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: #2196f3;
  }

  &:focus {
    outline: none;
    background-color: rgba(33, 150, 243, 0.1);
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Language Popup Styles - Fantastic Query Popup Style with Full Blur */
.language-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  z-index: 1999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-in-out;
}

.language-popup {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 600px;
  width: 90%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;
  z-index: 2001;
  border: 2px solid #3b82f6;
  pointer-events: auto;
  cursor: default;
  animation: slideIn 0.3s ease-out;
  margin: 0;

  &.dragging {
    transition: none !important;
    animation: none !important;
    box-shadow: 0 35px 70px -12px rgba(0, 0, 0, 0.4) !important;
  }

  /* When positioned with left/top after drag, switch to fixed positioning */
  &.positioned {
    position: fixed !important;
    top: auto !important;
    left: auto !important;
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  cursor: grab;
  user-select: none;

  &:active {
    cursor: grabbing;
  }

  &:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }

  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
  }

  p {
    margin: 2px 0 0 0;
    opacity: 0.9;
    font-size: 12px;
    color: white;
  }

  .center-btn,
  .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    opacity: 0.8;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      opacity: 1;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.popup-content {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.popup-field {
  margin-bottom: 20px;

  &.default-field {
    .popup-field-label {
      color: #2196f3;
      font-weight: 600;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.popup-field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  color: #333;

  .default-indicator {
    font-size: 12px;
    color: #2196f3;
    font-weight: normal;
    margin-left: 8px;
  }
}

.popup-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  transition: all 0.2s ease;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  &:hover:not(:disabled):not([readonly]) {
    border-color: #9ca3af;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
    font-style: italic;
  }

  &.professional-input {
    font-weight: 400;
  }

  &.professional-textarea {
    font-weight: 400;
    resize: vertical;
    min-height: 80px;
  }
}

/* Professional Field Enhancements */
.popup-field-label {
  .language-label-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .language-flag {
    font-size: 18px;
  }

  .language-name {
    font-weight: 500;
    color: #374151;
  }

  .default-indicator {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    margin-left: 8px;
  }
}

.popup-input-container {
  position: relative;
  margin-bottom: 24px;

  &.focused {
    .popup-input {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.character-count {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 11px;
  color: #6b7280;
  font-weight: 400;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;

  .footer-info {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
  }

  .footer-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .clear-btn,
  .cancel-btn,
  .save-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;

    mat-icon {
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }
  }

  .clear-btn {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #e2e8f0;
      color: #475569;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .cancel-btn {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #f1f5f9;
      color: #475569;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-input:not([type='checkbox']) {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }

  .language-main-input {
    padding-right: 40px !important;
  }

  .language-globe-btn {
    right: 6px;
    width: 28px;
    height: 28px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .language-popup {
    width: 95%;
    max-height: 90vh;
  }

  .popup-header,
  .popup-footer {
    padding: 16px 20px;
  }

  .popup-content {
    padding: 20px;
  }

  .popup-footer {
    flex-direction: column;
    gap: 8px;

    .save-btn,
    .cancel-btn {
      width: 100%;
      padding: 12px 20px;
    }
  }
}

@media (max-width: 480px) {
  .form-input:not([type='checkbox']) {
    padding: 8px 10px;
    font-size: 14px;
  }

  .language-main-input {
    padding-right: 35px !important;
  }

  .language-globe-btn {
    right: 4px;
    width: 24px;
    height: 24px;

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }

  .popup-header h4 {
    font-size: 16px;
  }

  .popup-field-label {
    font-size: 13px;
  }

  .popup-input {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* No Input Indicator Responsive - EXACT match */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* Animation for language switching */
.language-input {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
