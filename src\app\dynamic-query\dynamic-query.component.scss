:host {
  display: block;
  padding: 24px;
}

.query-builder-container {
  margin-bottom: 24px;
}

.filter-group-wrapper {
  margin-bottom: 16px;
}

.filter-group-card {
  .mat-card-header {
    justify-content: flex-end;
    padding: 8px 8px 0 8px;
  }

  .remove-group-btn {
    margin-left: auto;
  }
}

.logic-to-previous {
  margin: 0 16px 16px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding: 0 8px;
}

.filter-preview {
  margin-top: 24px;

  pre {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.results-container {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px 16px 24px;

  .mat-card-title {
    margin: 0;
  }
}

.results-table {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  
  // Responsive table styling
  @media (max-width: 768px) {
    font-size: 12px;
    
    .mat-header-cell {
      padding: 8px 4px;
      font-size: 11px;
      font-weight: 600;
    }
    
    .mat-cell {
      padding: 8px 4px;
      font-size: 11px;
    }
  }
  
  @media (max-width: 480px) {
    font-size: 10px;
    
    .mat-header-cell {
      padding: 6px 2px;
      font-size: 10px;
    }
    
    .mat-cell {
      padding: 6px 2px;
      font-size: 10px;
      word-break: break-word;
      max-width: 120px;
    }
  }
  
  // Enhanced header styling
  .mat-header-cell {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  // Enhanced row styling
  .mat-row {
    cursor: pointer;

    &:nth-child(even) {
      background-color: #f8f9fa;
    }

    &:hover {
      background-color: #e9ecef;
      cursor: pointer;
    }
  }
  
  // Cell styling
  .mat-cell {
    border-bottom: 1px solid #dee2e6;
    padding: 12px 8px;
  }

  // Ensure all table rows are clickable
  tr {
    cursor: pointer;

    &:hover {
      cursor: pointer;
    }
  }
}

// Responsive container for table
.results-container {
  margin-top: 24px;
  
  .mat-card {
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .mat-card-content {
    padding: 0;
    overflow-x: auto;
    
    // Custom scrollbar for better UX
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Mobile-first responsive design
@media (max-width: 768px) {
  :host {
    padding: 16px;
  }
  
  .results-container {
    margin-top: 16px;
  }
  
  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    
    .mat-card-title {
      font-size: 18px;
    }
    
    .download-button {
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  :host {
    padding: 12px;
  }
  
  .query-builder-container {
    margin-bottom: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 12px;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
  
  .results-header {
    padding: 12px;
    
    .mat-card-title {
      font-size: 16px;
    }
  }
}

// Large screen optimizations
@media (min-width: 1200px) {
  .results-table {
    .mat-header-cell,
    .mat-cell {
      padding: 16px 12px;
    }
  }
  
  .results-header {
    padding: 24px;
    
    .mat-card-title {
      font-size: 24px;
    }
  }
}

// Additional responsive enhancements
.table-wrapper {
  width: 100%;
  overflow-x: auto;
  position: relative;
}

.header-text,
.cell-content {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clickable-row {
  transition: background-color 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: #e3f2fd !important;
    cursor: pointer;
  }
}

// No data message styling
.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    color: #ccc;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 500;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
}

// Button text responsive behavior
.button-text {
  @media (max-width: 480px) {
    display: none;
  }
}

// Enhanced download button
.download-button {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  @media (max-width: 480px) {
    min-width: 48px;
    padding: 0 12px;
    
    mat-icon {
      margin: 0;
    }
  }
}

// Improved filter groups section responsiveness
.filter-groups-section {
  @media (max-width: 768px) {
    .filter-group-card {
      margin-bottom: 12px;
      
      .mat-card-content {
        padding: 12px;
      }
    }
    
    .logic-to-previous {
      margin: 0 8px 12px;
    }
  }
  
  @media (max-width: 480px) {
    .filter-group-card {
      .mat-card-header {
        padding: 4px;
      }
      
      .mat-card-content {
        padding: 8px;
      }
    }
    
    .logic-to-previous {
      margin: 0 4px 8px;
      
      .mat-form-field {
        width: 100%;
      }
    }
  }
}

// Filter preview responsive styling
.filter-preview {
  @media (max-width: 768px) {
    margin-top: 16px;
    
    .mat-card-content {
      padding: 12px;
    }
    
    pre {
      padding: 12px;
      font-size: 12px;
    }
  }
  
  @media (max-width: 480px) {
    pre {
      padding: 8px;
      font-size: 11px;
    }
  }
}