<!-- Language Field Component - Matches existing field styles with popup -->
<div class="form-field" [formGroup]="form">

  <!-- Field Label - EXACT match to existing style -->
  <label [for]="getUniqueFieldId()">
    {{ field.label || field.fieldName }}
    @if (field.mandatory) {
      <span>*</span>
    }
    @if (field.noInput) {
      <span class="no-input-indicator"> (Read Only)</span>
    }
  </label>

  <!-- Input Container with Globe Icon Inside -->
  <div class="language-input-container">

    <!-- Main Input Field - EXACT match to existing form-input style -->
    @if (field.type === 'textarea') {
      <textarea
        [id]="getUniqueFieldId()"
        [formControl]="getMainFormControl()"
        [placeholder]="field.placeholder || (field.label?.trim() || field.fieldName)"
        [readonly]="isViewMode || field.noInput"
        [disabled]="isViewMode || field.noInput"
        class="form-input language-main-input"
        rows="3">
      </textarea>
    } @else {
      <input
        [id]="getUniqueFieldId()"
        [type]="getInputType()"
        [formControl]="getMainFormControl()"
        [placeholder]="field.placeholder || (field.label?.trim() || field.fieldName)"
        [readonly]="isViewMode || field.noInput"
        [disabled]="isViewMode || field.noInput"
        class="form-input language-main-input">
    }

    <!-- Globe Icon Inside Field (only show if multiple languages) -->
    @if (showLanguageSelector && !isViewMode && !field.noInput) {
      <button
        type="button"
        class="language-globe-btn"
        [matTooltip]="getLanguageTooltip()"
        (click)="toggleLanguagePopup($event)">
        <mat-icon>language</mat-icon>
      </button>
    }
  </div>

  <!-- Language Popup -->
  @if (showLanguagePopup) {
    <div class="language-popup-overlay">
      <div class="language-popup" #languagePopupRef>

        <!-- Draggable Popup Header -->
        <div class="popup-header drag-handle" #dragHandle>
          <div style="display: flex; align-items: center; gap: 12px;">
            <span style="font-size: 18px; opacity: 0.8;">⋮⋮</span>
            <div>
              <h4>Edit in Multiple Languages</h4>
              <p style="margin: 2px 0 0 0; opacity: 0.9; font-size: 12px;">Manage content in {{ availableLanguages.length }} languages</p>
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <button type="button" class="center-btn" (click)="centerPopup()" title="Center Window">
              <span style="font-size: 16px;">⌖</span>
            </button>
            <button type="button" class="close-btn" (click)="closeLanguagePopup()" title="Close">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>

        <!-- Language Fields with Professional Styling -->
        <div class="popup-content">
          @for (language of availableLanguages; track language; let i = $index) {
            <div class="popup-field" [class.default-field]="i === 0">

              <!-- Language Label with Flag Icon -->
              <label class="popup-field-label">
                <div class="language-label-content">
                  <span class="language-flag">{{ getLanguageFlag(language) }}</span>
                  <span class="language-name">{{ getLanguageDisplayName(language) }}</span>
                  @if (i === 0) {
                    <span class="default-indicator">Default</span>
                  }
                </div>
              </label>

              <!-- Professional Language Input -->
              <div class="popup-input-container">
                @if (field.type === 'textarea') {
                  <textarea
                    [formControl]="languageControls[language]"
                    [placeholder]="'Enter ' + (field.label || field.fieldName) + ' in ' + getLanguageDisplayName(language)"
                    class="popup-input professional-textarea"
                    rows="3"
                    (input)="onLanguageInputChange(language, $any($event.target).value)"
                    (focus)="onInputFocus($event)"
                    (blur)="onInputBlur($event)">
                  </textarea>
                } @else {
                  <input
                    [type]="getInputType()"
                    [formControl]="languageControls[language]"
                    [placeholder]="'Enter ' + (field.label || field.fieldName) + ' in ' + getLanguageDisplayName(language)"
                    class="popup-input professional-input"
                    (input)="onLanguageInputChange(language, $any($event.target).value)"
                    (focus)="onInputFocus($event)"
                    (blur)="onInputBlur($event)">
                }

                <!-- Character Count -->
                <div class="character-count">
                  {{ getCharacterCount(language) }} characters
                </div>
              </div>
            </div>
          }
        </div>

        <!-- Professional Popup Footer -->
        <div class="popup-footer">
          <div class="footer-info">
            <span>{{ getModifiedLanguagesCount() }} of {{ availableLanguages.length }} languages modified</span>
          </div>
          <div class="footer-actions">
            <button type="button" class="clear-btn" (click)="clearAllLanguages()">
              <mat-icon>clear_all</mat-icon>
              Clear All
            </button>
            <button type="button" class="cancel-btn" (click)="closeLanguagePopup()">
              <mat-icon>close</mat-icon>
              Cancel
            </button>
            <button type="button" class="save-btn" (click)="saveLanguageChanges()">
              <mat-icon>check</mat-icon>
              Save Changes
            </button>
          </div>
        </div>

      </div>
    </div>
  }

</div>
