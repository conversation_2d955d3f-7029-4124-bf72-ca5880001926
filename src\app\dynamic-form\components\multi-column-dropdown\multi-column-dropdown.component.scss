@use '../../../../styles/shared.scss' as *;

/* Multi-Column Dropdown Container - Full width */
.multi-column-dropdown-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 8px;
}

/* Initial Input Specific Styling - Match Original Dropdown */
.multi-column-dropdown-container.initial-input-style {
  width: 100% !important;
  min-width: unset !important;
  flex: 1 !important;
}

.multi-column-dropdown-container.initial-input-style .form-input {
  width: 100% !important;
  min-width: unset !important;
  flex: 1 !important;
}

/* Ensure dropdown content matches original width in initial input */
.multi-column-dropdown-container.initial-input-style .dropdown-content {
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

/* Table styling adjustments for initial input */
.multi-column-dropdown-container.initial-input-style .dropdown-table {
  width: 100% !important;
  min-width: 100% !important;
}

/* Override any conflicting width styles when used in initial input */
.id-input-container .multi-column-dropdown-container {
  width: 100% !important;
  min-width: unset !important;
  flex: 1 !important;
}

.id-input-container .multi-column-dropdown-container .form-input {
  width: 100% !important;
  min-width: unset !important;
  flex: 1 !important;
}

.id-input-container .multi-column-dropdown-container .dropdown-content {
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
}

/* Input field styling */
.multi-column-dropdown-container .form-input {
  flex: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px 0 0 8px;
  border-right: none;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 8px 12px;
  outline: none;
  box-sizing: border-box;
}

/* Arrow button styling */
.multi-column-dropdown-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  margin-left: -1px;
  position: relative;
  flex-shrink: 0;
}

.multi-column-dropdown-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.multi-column-dropdown-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Focus states */
.multi-column-dropdown-container:focus-within {
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  border-radius: 8px;
}

.multi-column-dropdown-container:focus-within .form-input {
  outline: none;
  border-color: #9e9e9e;
}

.multi-column-dropdown-container:focus-within .dropdown-arrow-btn {
  border-color: #9e9e9e;
}

/* Enhanced dropdown content container - Perfect form color matching */
.dropdown-content {
  position: absolute;
  top: 100%; /* No gap - seamlessly connected */
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* Clean white to subtle gray gradient */
  border: 2px solid #e2e8f0; /* Softer border matching form fields */
  border-top: none; /* Remove top border for seamless connection */
  border-radius: 0 0 12px 12px; /* Only bottom corners rounded */
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.08),
    0 10px 20px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Enhanced layered shadows with inner highlight */
  max-height: 400px; /* Reasonable height for content */
  overflow: hidden; /* Container doesn't scroll, children do */
  backdrop-filter: blur(16px) saturate(200%); /* Enhanced glass effect */
  padding: 0 !important; /* ABSOLUTELY NO PADDING */
  margin: 0 !important; /* ABSOLUTELY NO MARGINS */
  box-sizing: content-box; /* Don't include borders in width calculation */
}

/* Loading state */
.dropdown-loading {
  padding: 16px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-icon {
  animation: spin 1s linear infinite;
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Table container - Proper layout without overlap */
.dropdown-table-container {
  height: 300px; /* FIXED height - not max-height */
  width: 100%; /* FIXED width */
  overflow: scroll; /* ALWAYS show both scrollbars */
  overflow-x: scroll; /* ALWAYS show horizontal scrollbar */
  overflow-y: scroll; /* ALWAYS show vertical scrollbar */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: auto; /* Firefox - visible scrollbar */
  scrollbar-color: #64748b #f1f5f9; /* Enhanced scrollbar colors matching form */
  scrollbar-gutter: stable;
  padding: 0; /* No padding */
  margin: 0; /* No margins */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* Subtle gradient matching form */
  border-left: none; /* No left border */
  border-right: none; /* No right border */
  position: relative;
  box-sizing: border-box; /* Include borders in width calculation */
}



/* Table styling - Proper layout without overlap */
.dropdown-table {
  width: 100%; /* Full width without extension */
  min-width: 600px; /* Minimum width to trigger horizontal scroll */
  border-collapse: collapse;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  margin: 0; /* No margins */
  padding: 0; /* No padding */
  border-spacing: 0; /* No border spacing */
  table-layout: auto; /* Auto layout for proper column sizing */
  box-sizing: border-box; /* Include borders in width calculation */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* Subtle gradient matching form */
  border-left: none; /* No left border */
  border-right: none; /* No right border */
  position: relative; /* For precise positioning */
}

/* Enhanced table headers - Proper coverage without overlap */
.table-header {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); /* Enhanced gradient matching form */
  border-bottom: 2px solid #cbd5e1; /* Softer border matching form */
  border-left: none; /* No left border */
  border-right: none; /* No right border */
  border-top: none; /* No top border */
  padding: 16px 12px; /* Balanced padding for proper coverage */
  text-align: left;
  font-weight: 500; /* Medium weight */
  color: #475569; /* Darker, more readable - matches form text */
  font-size: 13px; /* Slightly smaller for elegance */
  letter-spacing: 0.05em; /* Wider letter spacing for headers */
  text-transform: uppercase; /* Modern header style */
  white-space: normal; /* Allow text wrapping if needed */
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Subtle shadow with inner highlight */
  margin: 0; /* No margins */
  min-width: 120px; /* Minimum width for proper coverage */
  width: auto; /* Auto width based on content */
  vertical-align: middle; /* Center align header content */
}

/* First header - Proper edge attachment */
.table-header:first-child {
  padding-left: 8px; /* Minimal left padding */
  border-left: none;
  border-top-left-radius: 0;
}

/* Last header - Proper edge attachment */
.table-header:last-child {
  padding-right: 8px; /* Minimal right padding */
  border-right: none;
  border-top-right-radius: 0;
}

/* Table rows - Enhanced with keyboard navigation */
.table-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
  outline: none;
}

.table-row:hover,
.table-row:focus {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); /* Enhanced blue gradient matching form focus */
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 2px 4px rgba(59, 130, 246, 0.1); /* Enhanced layered shadows */
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:active {
  background-color: #e9ecef;
}

/* Enhanced table cells - Proper width and text display */
.table-cell {
  padding: 14px 12px; /* Balanced padding for better text display */
  color: #475569; /* Darker, more readable - matches form text */
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  white-space: normal; /* Allow text wrapping for better display */
  overflow: visible; /* Show full content */
  text-overflow: clip; /* Don't truncate short values */
  min-width: 120px; /* Minimum width for proper display */
  max-width: none; /* Remove max-width restriction */
  width: auto; /* Auto width based on content */
  letter-spacing: 0.01em; /* Subtle letter spacing */
  line-height: 1.4; /* Better line height for readability */
  border-left: none !important; /* REMOVE left border completely */
  border-right: none !important; /* REMOVE right border completely */
  margin: 0 !important; /* ABSOLUTELY NO MARGINS */
  background: transparent; /* Transparent to show table background */
  vertical-align: top; /* Align content to top */
}

/* First cell - Proper edge attachment */
.table-cell:first-child {
  padding-left: 8px; /* Minimal left padding */
  border-left: none;
}

/* Last cell - Proper edge attachment */
.table-cell:last-child {
  padding-right: 8px; /* Minimal right padding */
  border-right: none;
}

/* Single column list fallback - ALWAYS show scrollbar regardless of content */
.dropdown-list {
  height: 300px; /* FIXED height - not max-height */
  overflow-y: scroll; /* ALWAYS show vertical scrollbar */
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: auto; /* Firefox - visible scrollbar */
  scrollbar-color: #888888 #f1f1f1; /* Firefox - darker for visibility */
  scrollbar-gutter: stable;
}

/* Enhanced dropdown items - Proper text display */
.dropdown-item {
  padding: 14px 12px; /* Balanced padding for better text display */
  cursor: pointer;
  border-bottom: 1px solid #e2e8f0; /* Softer border matching form */
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #475569; /* Darker, more readable - matches form text */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth modern transition */
  outline: none;
  display: flex;
  align-items: center;
  min-height: 48px; /* Taller for better touch targets */
  letter-spacing: 0.01em;
  margin: 0; /* No margins */
  background: transparent; /* Transparent to show container background */
  white-space: normal; /* Allow text wrapping */
  overflow: visible; /* Show full content */
  text-overflow: clip; /* Don't truncate text */
}

.dropdown-item:hover,
.dropdown-item:focus {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); /* Enhanced blue gradient matching form focus */
  color: #1e40af; /* Darker blue text for better contrast */
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 2px 4px rgba(59, 130, 246, 0.1); /* Enhanced layered shadows */
}

/* First dropdown item - Proper edge attachment */
.dropdown-item:first-child {
  padding-left: 8px; /* Minimal left padding */
  border-radius: 10px 10px 0 0; /* Round top corners */
}

/* Last dropdown item - Proper edge attachment */
.dropdown-item:last-child {
  padding-right: 8px; /* Minimal right padding */
  border-bottom: none;
  border-radius: 0 0 10px 10px; /* Round bottom corners */
}

.dropdown-item:active {
  background-color: #e9ecef;
}

/* Empty state */
.dropdown-empty {
  padding: 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* Disabled state */
.multi-column-dropdown-container.disabled .form-input,
.multi-column-dropdown-container .form-input.disabled,
.multi-column-dropdown-container .form-input[disabled],
.multi-column-dropdown-container .form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

.multi-column-dropdown-container.disabled .dropdown-arrow-btn,
.multi-column-dropdown-container .dropdown-arrow-btn[disabled] {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.multi-column-dropdown-container.disabled .dropdown-arrow-btn .mat-icon,
.multi-column-dropdown-container .dropdown-arrow-btn[disabled] .mat-icon {
  color: #5f6368 !important;
}

/* ALWAYS VISIBLE scrollbar styling for multi-column dropdown */
.dropdown-table-container::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar {
  width: 12px !important; /* Wider for better visibility */
  height: 12px !important; /* Taller for horizontal scrollbar */
  background: #f8f9fa !important; /* Light background */
  display: block !important; /* Force display */
  -webkit-appearance: none !important; /* Remove default styling */
}

.dropdown-table-container::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-track {
  background: #e9ecef !important; /* Visible track */
  border-radius: 6px;
  margin: 2px;
  border: 1px solid #dee2e6; /* Border for definition */
  display: block !important; /* Force display */
}

.dropdown-table-container::-webkit-scrollbar-thumb,
.dropdown-list::-webkit-scrollbar-thumb {
  background: #888888 !important; /* Darker for visibility */
  border-radius: 6px;
  border: 2px solid #e9ecef; /* Border for definition */
  min-height: 30px !important; /* Larger minimum thumb size */
  min-width: 30px !important; /* Larger minimum thumb width */
  display: block !important; /* Force display */
}

.dropdown-table-container::-webkit-scrollbar-thumb:hover,
.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #666666 !important; /* Darker on hover */
}

.dropdown-table-container::-webkit-scrollbar-thumb:active,
.dropdown-list::-webkit-scrollbar-thumb:active {
  background: #444444 !important; /* Even darker when active */
}

.dropdown-table-container::-webkit-scrollbar-corner,
.dropdown-list::-webkit-scrollbar-corner {
  background: #e9ecef !important; /* Visible corner */
  border: 1px solid #dee2e6;
  display: block !important; /* Force display */
}

/* Mobile scrollbar styling - Still visible but smaller */
@media (max-width: 768px) {
  .dropdown-table-container::-webkit-scrollbar,
  .dropdown-list::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .dropdown-table-container::-webkit-scrollbar,
  .dropdown-list::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
}

/* Fallback for Firefox and other browsers */
@supports not selector(::-webkit-scrollbar) {
  .dropdown-table-container,
  .dropdown-list {
    scrollbar-width: auto; /* Use browser default scrollbar width */
    scrollbar-color: #c1c1c1 #f1f1f1;
  }
}

/* Force scrollbar visibility across browsers */
.dropdown-table-container,
.dropdown-list {
  scrollbar-gutter: stable;
  /* Force scrollbar to always be present */
  padding-right: 0; /* Remove any padding that might hide scrollbar */
}

/* Force webkit scrollbars to be visible */
.dropdown-table-container::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar {
  -webkit-appearance: none;
  display: block !important;
}

/* Scrollbars are always rendered regardless of content amount */

/* FORCE scrollbar track and thumb to show even with minimal content */
.dropdown-table-container::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-track {
  background: #e9ecef !important;
  display: block !important;
  visibility: visible !important;
}

.dropdown-table-container::-webkit-scrollbar-thumb,
.dropdown-list::-webkit-scrollbar-thumb {
  background: #888888 !important;
  display: block !important;
  visibility: visible !important;
  /* Force thumb to appear even when no scrolling needed */
  min-height: 50px !important;
  min-width: 50px !important;
}

/* ULTIMATE FORCE SCROLLBAR VISIBILITY - Override ALL hiding */
.dropdown-table-container,
.dropdown-list {
  /* Force scrollbar to be visible on all systems */
  scrollbar-width: auto !important; /* Firefox */
  -ms-overflow-style: scrollbar !important; /* IE/Edge */
  /* Override macOS/iOS scrollbar hiding */
  -webkit-overflow-scrolling: auto !important;
  /* Force scrollbars to never hide */
  overflow: scroll !important;
  overflow-x: scroll !important;
  overflow-y: scroll !important;
  /* Override any CSS that might hide scrollbars */
  scrollbar-gutter: stable both-edges !important;
}

/* Force scrollbar elements to be visible */
.dropdown-table-container::-webkit-scrollbar,
.dropdown-table-container::-webkit-scrollbar-track,
.dropdown-table-container::-webkit-scrollbar-thumb,
.dropdown-list::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-thumb {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  pointer-events: auto !important;
}

/* Ensure scrollbars are always visible */
.dropdown-table-container::-webkit-scrollbar,
.dropdown-list::-webkit-scrollbar {
  -webkit-appearance: none; /* Remove default styling */
  display: block !important; /* Force display */
}

.dropdown-table-container::-webkit-scrollbar-track,
.dropdown-list::-webkit-scrollbar-track {
  -webkit-appearance: none;
  display: block !important;
}

.dropdown-table-container::-webkit-scrollbar-thumb,
.dropdown-list::-webkit-scrollbar-thumb {
  -webkit-appearance: none;
  display: block !important;
}

/* Enhanced Responsive design */
@media (max-width: 768px) {
  .dropdown-content {
    max-height: 350px; /* Slightly smaller on tablets */
  }

  .table-cell {
    padding: 8px 6px;
    font-size: 13px;
    max-width: 120px;
  }

  .table-header {
    padding: 10px 6px;
    font-size: 13px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .multi-column-dropdown-container {
    min-width: 150px; /* Reduce minimum width on tablets */
  }

  .multi-column-dropdown-container .form-input {
    font-size: 14px;
    padding: 6px 10px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}

@media (max-width: 480px) {
  .dropdown-content {
    max-height: 300px; /* Smaller on mobile */
  }

  .table-cell {
    padding: 6px 4px;
    font-size: 12px;
    max-width: 100px;
  }

  .table-header {
    padding: 8px 4px;
    font-size: 12px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .multi-column-dropdown-container {
    min-width: 120px; /* Further reduce on mobile */
  }

  .multi-column-dropdown-container .form-input {
    font-size: 13px;
    padding: 4px 8px;
    height: 36px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }

  /* Enhanced table scrolling on mobile */
  .dropdown-table-container {
    overflow-x: auto; /* Ensure horizontal scroll works on mobile */
  }

  .dropdown-table {
    min-width: 300px; /* Minimum table width to trigger horizontal scroll */
  }
}

@media (max-width: 360px) {
  .dropdown-content {
    max-height: 250px; /* Even smaller on very small screens */
  }

  .table-cell {
    padding: 4px 2px;
    font-size: 11px;
    max-width: 80px;
  }

  .table-header {
    padding: 6px 2px;
    font-size: 11px;
  }

  .dropdown-item {
    padding: 6px 8px;
    font-size: 11px;
  }

  .multi-column-dropdown-container {
    min-width: 100px;
  }

  .multi-column-dropdown-container .form-input {
    font-size: 12px;
    padding: 2px 6px;
    height: 32px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn {
    width: 28px;
    height: 28px;
  }

  .multi-column-dropdown-container .dropdown-arrow-btn .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }

  /* Optimize table for very small screens */
  .dropdown-table {
    min-width: 250px; /* Smaller minimum width */
  }

  .table-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60px; /* Very compact cells */
  }
}

/* View List Footer */
.view-list-footer {
  padding: 12px;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 0 12px 12px;
}

.view-list-footer-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  min-width: 180px;
}

.view-list-footer-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.view-list-footer-btn .mat-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
}

.view-list-footer-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
