import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { MultiColumnDropdownComponent } from './multi-column-dropdown.component';
import { MultiColumnDropdownService } from '../../services/multi-column-dropdown.service';
import { KeycloakService } from '../../../services/keycloak.service';

describe('MultiColumnDropdownComponent', () => {
  let component: MultiColumnDropdownComponent;
  let fixture: ComponentFixture<MultiColumnDropdownComponent>;
  let mockMultiColumnService: jasmine.SpyObj<MultiColumnDropdownService>;
  let mockKeycloakService: jasmine.SpyObj<KeycloakService>;

  const mockConfig = {
    queryBuilderId: 'country',
    placeholder: 'Select country',
    emptyMessage: 'No countries found',
    tooltip: 'Show countries',
    limit: 20
  };

  const mockColumnData = {
    columns: [
      { fieldName: 'ID', label: 'ID', column: 1 },
      { fieldName: 'countryName', label: 'Country Name', column: 2 },
      { fieldName: 'countryCode', label: 'Country Code', column: 3 }
    ],
    options: [
      { ID: 'AF', countryName: 'Afghanistan', countryCode: '+93' },
      { ID: 'AL', countryName: 'Albania', countryCode: '+355' }
    ],
    hasMultipleColumns: true
  };

  beforeEach(async () => {
    const multiColumnServiceSpy = jasmine.createSpyObj('MultiColumnDropdownService', [
      'loadDropdownData',
      'getCachedData',
      'getCachedColumnDef',
      'filterOptionsClientSide',
      'getOptionDisplayText',
      'getOptionValue'
    ]);

    const keycloakServiceSpy = jasmine.createSpyObj('KeycloakService', ['getToken']);

    await TestBed.configureTestingModule({
      imports: [
        MultiColumnDropdownComponent,
        HttpClientTestingModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MultiColumnDropdownService, useValue: multiColumnServiceSpy },
        { provide: KeycloakService, useValue: keycloakServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MultiColumnDropdownComponent);
    component = fixture.componentInstance;
    mockMultiColumnService = TestBed.inject(MultiColumnDropdownService) as jasmine.SpyObj<MultiColumnDropdownService>;
    mockKeycloakService = TestBed.inject(KeycloakService) as jasmine.SpyObj<KeycloakService>;

    // Setup component inputs
    component.fieldName = 'country';
    component.formControl = new FormControl('');
    component.config = mockConfig;

    // Setup service mocks
    mockMultiColumnService.loadDropdownData.and.returnValue(of(mockColumnData));
    mockMultiColumnService.getCachedData.and.returnValue(null);
    mockMultiColumnService.getCachedColumnDef.and.returnValue(null);
    mockMultiColumnService.getOptionDisplayText.and.returnValue('Afghanistan');
    mockMultiColumnService.getOptionValue.and.returnValue('AF');
    mockKeycloakService.getToken.and.returnValue('mock-token');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with unique ID', () => {
    component.ngOnInit();
    expect(component.uniqueId).toContain('country_');
  });

  it('should load dropdown data on initialization', () => {
    component.ngOnInit();
    expect(mockMultiColumnService.loadDropdownData).toHaveBeenCalledWith('country', undefined, 20);
  });

  it('should toggle dropdown visibility', () => {
    spyOn(component.dropdownToggle, 'emit');
    
    component.toggleDropdown();
    expect(component.dropdownToggle.emit).toHaveBeenCalledWith(true);
  });

  it('should handle option selection', () => {
    spyOn(component.valueChange, 'emit');
    spyOn(component.optionSelect, 'emit');
    
    const option = { ID: 'AF', countryName: 'Afghanistan', countryCode: '+93' };
    component.columns = mockColumnData.columns;
    
    component.selectOption(option);
    
    expect(component.optionSelect.emit).toHaveBeenCalledWith(option);
    expect(component.valueChange.emit).toHaveBeenCalled();
  });

  it('should handle search input changes', () => {
    spyOn(component.searchChange, 'emit');
    
    const event = { target: { value: 'test' } } as any;
    component.onInputChange(event);
    
    expect(component.searchChange.emit).toHaveBeenCalledWith('test');
  });

  it('should process column data correctly', () => {
    component['processColumnData'](mockColumnData);
    
    expect(component.columns).toEqual(mockColumnData.columns);
    expect(component.filteredOptions).toEqual(mockColumnData.options);
    expect(component.hasMultipleColumns).toBe(true);
  });

  it('should handle disabled state', () => {
    component.isDisabled = true;
    component.updateFormControlDisabledState();
    
    expect(component.formControl.disabled).toBe(true);
  });

  it('should handle readonly state', () => {
    component.isReadonly = true;
    component.updateFormControlDisabledState();
    
    expect(component.formControl.disabled).toBe(true);
  });

  it('should return correct CSS classes', () => {
    component.cssClass = 'custom-class';
    expect(component.inputClass).toContain('form-input');
    expect(component.inputClass).toContain('custom-class');
  });

  it('should return correct arrow icon', () => {
    component.showDropdown = false;
    expect(component.dropdownArrowIcon).toBe('keyboard_arrow_down');
    
    component.showDropdown = true;
    expect(component.dropdownArrowIcon).toBe('keyboard_arrow_up');
  });

  it('should handle empty state', () => {
    component['setEmptyDropdownState']();
    expect(component.filteredOptions).toEqual([]);
    expect(component.showDropdown).toBe(true);
  });

  it('should cleanup on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');
    
    component.ngOnDestroy();
    
    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
  });

  it('should track columns correctly', () => {
    const column = { fieldName: 'ID', label: 'ID', column: 1 };
    expect(component.trackByColumn(0, column)).toBe('ID');
  });

  it('should track options correctly', () => {
    const option = { ID: 'AF', countryName: 'Afghanistan' };
    expect(component.trackByOption(0, option)).toBe('AF');
  });
});
