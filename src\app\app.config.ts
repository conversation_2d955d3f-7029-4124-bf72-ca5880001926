import { ApplicationConfig, provideZoneChangeDetection, APP_INITIALIZER } from '@angular/core';
import { provideRouter, withPreloading, PreloadAllModules, withViewTransitions } from '@angular/router';
import { provideHttpClient , withInterceptors } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { privilegesInterceptor } from './interceptors/privileges.interceptor';
import { keycloakTokenInterceptor } from './interceptors/keycloak-token.interceptor';
import { languagesInterceptor } from './interceptors/languages.interceptor';
import { sessionStorageInterceptor } from './interceptors/session-storage.interceptor';
import { KeycloakIntegrationService } from './services/keycloak-integration.service';
import { routes } from './app.routes';

function appInitializerFactory(keycloakIntegration: KeycloakIntegrationService) {
  return async () => {
    await keycloakIntegration.initializeKeycloak();
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes,
      withPreloading(PreloadAllModules)
   ,withViewTransitions() ),
    provideHttpClient(withInterceptors([
      keycloakTokenInterceptor,      // First: Add Keycloak token
      sessionStorageInterceptor,      // Second: Add session data (languages, privileges, etc.)
      privilegesInterceptor,          // Third: Legacy privileges support
      languagesInterceptor           // Fourth: Legacy languages support
    ])),
    provideAnimationsAsync(),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [KeycloakIntegrationService],
      multi: true
    }
  ]
};
